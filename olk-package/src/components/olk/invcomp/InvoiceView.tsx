// 'use client';
// import React, { useState, useEffect } from 'react';
// import { Card } from 'primereact/card';
// import { DataTable } from 'primereact/datatable';
// import { Column } from 'primereact/column';
// import { ProgressSpinner } from 'primereact/progressspinner';
// import { Message } from 'primereact/message';
// import { Fieldset } from 'primereact/fieldset';
// import { Panel } from 'primereact/panel';
// import { Divider } from 'primereact/divider';
// import { AxiosError } from 'axios';
// import { getOrgDataField } from '../../../utils/cookies';
// import { useEnvContext } from "../../../contexts/EnvContextProvider";
// import "../../../styles/olkcss.scss";
// import { Button } from 'primereact/button';
// import { PDFDownloadLink } from '@react-pdf/renderer';
// import InvoicePDF from './InvoicePdf';
// import apiCall from '../../../utils/apiCallService';

// // Define invoice preferences interface
// interface InvoicePrefs {
//     logo?: string;
//     sign?: string;
//     autoinvno?: number;
//     tandc?: string;
// }

// export interface orgFields {
//     bankdetails: null | string;
//     gstin: string;
//     orgpan: string;
//     orgaddr: string;
//     orgpincode: string;
//     invoice_preferences?: InvoicePrefs;
// }

// export interface details {
//     bisdetails: orgFields; // Match InvoicePdf.tsx expectation
//     olkstatus?: number; // Matches JSON structure
// }

// export interface InvoiceContent {
//     gsflag: number;
//     gstflag: number;
//     gstrate: number;
//     quantity: number;
//     gstamount: number;
//     productcode: number;
//     productname: string;
//     freeQuantity: number;
//     pricePerUnit: number;
//     productAmount: number;
//     taxableAmount: number;
//     discountAmount: number;
// }

// export interface InvoiceParty {
//     custname: string;
//     custaddr: string;
//     custphone: string;
//     custemail: string | null;
//     state: string;
//     pincode: string;
//      gstin: {
//     [stateCode: string]: string;
//   };
// }

// export interface Consignee {
//     name?: string;
//     state?: string;
//     address?: string;
//     pincode?: string;
// }

// export interface InvoiceRecord {
//     invid: number;
//     invoiceno: string;
//     ewaybillno: string | null;
//     invoicedate: string;
//     invnarration: string | null;
//     taxflag: number;
//     contents: InvoiceContent[];
//     amountpaid: string;
//     invoicetotal: string;
//     icflag: number;
//     roundoffflag: number;
//     lockflag: number;
//     discflag: number;
//     taxstate: string | null;
//     sourcestate: string | null;
//     orgstategstin: string | null;
//     attachment: string | null;
//     attachmentcount: number;
//     orgcode: number;
//     custid: number;
//     consignee: Consignee | null;
//     reverserecharge: string | null;
//     bankdetails: string | null;
//     transportationmode: string | null;
//     vehicleno: string | null;
//     dateofsupply: string | null;
//     paymentmode: number;
//     reversecharge: number | null;
//     address: string | null;
//     pincode: string | null;
//     inoutflag: number;
//     originaldate: string | null;
//     invoicetotalword: string | null;
// }

// export interface ApiResponse {
//     invrecord: InvoiceRecord;
//     custdetails?: InvoiceParty; // Made optional to handle potential absence
// }

// interface CombinedData extends ApiResponse {
//     orgDetails: details | null;
// }

// interface InvoiceViewProps {
//     invoiceId: string;
// }

// const InvoiceView: React.FC<InvoiceViewProps> = ({ invoiceId }) => {
//     const { OLK_PATH } = useEnvContext();
//     const [invoice, setInvoice] = useState<ApiResponse | null>(null);
//     const [combinedData, setCombinedData] = useState<CombinedData | null>(null);
//     const [loading, setLoading] = useState<boolean>(true);
//     const [error, setError] = useState<string | null>(null);
//     const orgname = getOrgDataField("orgname");
//     const orgCode = Number(getOrgDataField("orgcode"));

//     useEffect(() => {
//         const fetchInvoice = async () => {
//             if (!OLK_PATH) {
//                 setError("API base URL is not configured");
//                 setLoading(false);
//                 return;
//             }

//             if (!invoiceId) {
//                 setError("No invoice ID provided");
//                 setLoading(false);
//                 return;
//             }

//             setLoading(true);
//             setError(null);
//             try {
//                 const response = await apiCall<ApiResponse>("GET", `${OLK_PATH}/invoice/findinvoice?invid=${invoiceId}`);
//                 if (!response.data?.invrecord) {
//                     throw new Error("No invoice record found");
//                 }
//                 setInvoice(response.data);
//             } catch (err: unknown) {
//                 let errorMessage = "Failed to fetch invoice details.";
//                 if (err instanceof AxiosError) {
//                     if (err.response?.status === 404) {
//                         errorMessage = `Invoice not found for invid: ${invoiceId}`;
//                     } else {
//                         errorMessage = `Failed to fetch invoice: ${err.message}`;
//                     }
//                 } else if (err instanceof Error) {
//                     errorMessage = `Failed to fetch invoice: ${err.message}`;
//                 }

//                 setError(errorMessage);
//                 console.error("Error details:", err);
//             } finally {
//                 setLoading(false);
//             }
//         };

//         fetchInvoice();
//     }, [invoiceId, OLK_PATH]);

//     // Function to fetch orgDetails and combine with invoice
//     useEffect(() => {
//         const fetchOrgDetails = async () => {
//             if (!invoice) {
//                 return;
//             }

//             if (!OLK_PATH || !orgCode) {
//                 console.error("Missing OLK_PATH or orgCode");
//                 return;
//             }

//             try {
//                 const orgResponse = await apiCall<details>("GET", `${OLK_PATH}/organisations/bdt?orgcode=${orgCode}`);
//                 const combined: CombinedData = {
//                     ...invoice,
//                     orgDetails: orgResponse.data,
//                 };
//                 setCombinedData(combined);
//             } catch (err: unknown) {
//                 console.error("Error fetching orgDetails:", err);
//                 // Set combined data without org details to prevent blocking
//                 setCombinedData({
//                     ...invoice,
//                     orgDetails: null,
//                 });
//             }
//         };

//         // Only fetch org details if we have invoice data
//         if (invoice && invoice.invrecord) {
//             fetchOrgDetails();
//         }
//     }, [invoice, OLK_PATH, orgCode]);

//     if (loading) {
//         return (
//             <ProgressSpinner
//                 style={{ display: "block", margin: "2rem auto" }}
//             />
//         );
//     }

//     if (error) {
//         return (
//             <Message
//                 severity="error"
//                 text={error}
//                 style={{ display: "block", margin: "2rem" }}
//             />
//         );
//     }

//     if (!invoice || !invoice.invrecord) {
//         return (
//             <Message
//                 severity="warn"
//                 text="No invoice data available."
//                 style={{ display: "block", margin: "2rem" }}
//             />
//         );
//     }

//     return (
//         <div className="invoice-view p-m-4">
//             <Card
//                 title={
//                     invoice.invrecord.inoutflag === 15
//                         ? "Sales Invoice"
//                         : invoice.invrecord.inoutflag === 9
//                         ? "Purchase Invoice"
//                         : "Unknown Invoice"
//                 }
//             >
//                 {" "}
//                 <div className="invoice-header ">
//                     <div className="invoice-header">{orgname}</div>
//                     <div className="invoice-header">{`Invoice No : ${invoice.invrecord.invoiceno}`}</div>
//                     <div className="invoice-header">
//                         {`Invoice Date : ${new Date(
//                             invoice.invrecord.invoicedate
//                         )
//                             .toLocaleDateString("en-GB", {
//                                 day: "2-digit",
//                                 month: "2-digit",
//                                 year: "numeric",
//                             })
//                             .split("/")
//                             .join("-")}`}
//                     </div>
//                 </div>
//                 <Divider />
//                 {/* Customer and Consignee Details */}
//                 <div className="p-grid invoice-section">
//                     <div className="p-col-6 p-md-6">
//                         <Fieldset
//                             legend={
//                                 invoice.invrecord.inoutflag === 15
//                                     ? "Customer Details"
//                                     : "Supplier details"
//                             }
//                             className="p-shadow-2"
//                         >
//                             <div className="p-field">
//                                 <label>
//                                     <strong>Name : </strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     {invoice.custdetails?.custname || "N/A"}
//                                 </span>
//                             </div>

//                             <div className="p-grid p-formgrid">
//                                 <div className="p-col-3 p-field">
//                                     <label>
//                                         <strong>Phone : </strong>
//                                     </label>
//                                     <span className="ml-2">
//                                         {invoice.custdetails?.custphone ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-col-3 p-field">
//                                     <label>
//                                         <strong>Email : </strong>
//                                     </label>
//                                     <span className="ml-2">
//                                         {invoice.custdetails?.custemail ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                             </div>

//                             <div className="p-field">
//                                 <label>
//                                     <strong>Address : </strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     {invoice.custdetails?.custaddr || "N/A"}
//                                 </span>
//                             </div>

//                             <div className="p-grid p-formgrid">
//                                 <div className="p-col-3 p-field">
//                                     <label>
//                                         <strong>State : </strong>
//                                     </label>
//                                     <span className="ml-2">
//                                         {invoice.custdetails?.state || "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-col-3 p-field">
//                                     <label>
//                                         <strong>Pincode : </strong>
//                                     </label>
//                                     <span className="ml-2">
//                                         {invoice.custdetails?.pincode || "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-col-3 p-field">
//                                     <label>
//                                         <strong>GSTIN : </strong>
//                                     </label>
//                                     <span className="ml-2">
//                                     {Object.values(invoice.custdetails?.gstin || {})[0] || "N/A"}
//                                     </span>
//                                 </div>
//                             </div>
//                         </Fieldset>
//                     </div>
//                     <Divider />
//                     {invoice.invrecord.consignee && (
//                         <div className="p-col-12 p-md-6">
//                             <Fieldset
//                                 legend="Consignee Details"
//                                 className="p-shadow-2"
//                             >
//                                 <div className="p-field">
//                                     <label className="p-d-block">
//                                         <strong>Consignee Name : </strong>
//                                     </label>
//                                     <span>
//                                         {invoice.invrecord.consignee?.name ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-field">
//                                     <label className="p-d-block">
//                                         <strong>Consignee Address : </strong>
//                                     </label>
//                                     <span>
//                                         {invoice.invrecord.consignee?.address ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-field">
//                                     <label className="p-d-block">
//                                         <strong>State : </strong>
//                                     </label>
//                                     <span>
//                                         {invoice.invrecord.consignee?.state ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                                 <div className="p-field">
//                                     <label className="p-d-block">
//                                         <strong>Pincode : </strong>
//                                     </label>
//                                     <span>
//                                         {invoice.invrecord.consignee?.pincode ||
//                                             "N/A"}
//                                     </span>
//                                 </div>
//                             </Fieldset>
//                         </div>
//                     )}
//                 </div>
//                 <Divider />
//                 {/* Product Table */}
//                 <Panel header="Products" className="p-mb-2 p-shadow-2">
//                     <DataTable
//                         value={invoice.invrecord.contents}
//                         responsiveLayout="stack"
//                         className="p-datatable-sm invoice-products-table"
//                     >
//                         <Column
//                             field="productname"
//                             header="Product Name"
//                             className="p-text-bold"
//                         />
//                         <Column
//                             field="quantity"
//                             header="Quantity"
//                             align="right"
//                         />
//                         <Column
//                             field="freeQuantity"
//                             header="Free Qty"
//                             align="right"
//                         />
//                         <Column
//                             field="pricePerUnit"
//                             header="Price/Unit"
//                             align="right"
//                             body={(rowData) =>
//                                 `₹${rowData.pricePerUnit.toFixed(2)}`
//                             }
//                         />
//                         <Column
//                             field="discountAmount"
//                             header="Discount"
//                             align="right"
//                             body={(rowData) =>
//                                 invoice.invrecord.discflag === 1
//                                     ? `₹${(rowData.discountAmount || 0).toFixed(
//                                           2
//                                       )}`
//                                     : `${rowData.discountPercent || 0}%`
//                             }
//                         />
//                         <Column
//                             field="taxableAmount"
//                             header="Taxable Amt"
//                             align="right"
//                             body={(rowData) =>
//                                 `₹${rowData.taxableAmount.toFixed(2)}`
//                             }
//                         />

//                         <Column
//                             field="gstrate"
//                             header="GST Rate"
//                             align="right"
//                             body={(rowData) => `${rowData.gstrate}%`}
//                         />
//                         <Column
//                             field="gstamount"
//                             header="GST Amount"
//                             align="right"
//                             body={(rowData) =>
//                                 `₹${rowData.gstamount.toFixed(2)}`
//                             }
//                         />

//                         <Column
//                             field="productAmount"
//                             header="Total Amount"
//                             align="right"
//                             body={(rowData) =>
//                                 `₹${rowData.productAmount.toFixed(2)}`
//                             }
//                         />
//                     </DataTable>
//                 </Panel>
//                 <Divider />
//                 {/* Payment Details and Invoice Summary Side by Side */}
//                 <div className="p-grid">
//                     {/* Payment Details */}
//                     <div className="p-col-12 p-md-6">
//                         <Panel header="Payment Details" className="p-shadow-2">
//                             <div className="p-grid">
//                                 <div className="p-col-12 p-md-6">
//                                     <div className="p-field">
//                                         <label>
//                                             <strong>Payment Mode:</strong>
//                                         </label>
//                                         <span className="ml-2">
//                                             {invoice.invrecord.paymentmode ===
//                                             15
//                                                 ? "On Credit"
//                                                 : invoice.invrecord
//                                                       .paymentmode === 3
//                                                 ? "Cash Received"
//                                                 : invoice.invrecord
//                                                       .paymentmode === 2
//                                                 ? "Bank Transfer"
//                                                 : "Unknown"}
//                                         </span>
//                                     </div>
//                                 </div>
//                                 <div className="p-col-12 p-md-6">
//                                     <div className="p-field">
//                                         <label>
//                                             <strong>Bank Details:</strong>
//                                         </label>
//                                         <span className="ml-2">
//                                             {invoice.invrecord.bankdetails ||
//                                                 "N/A"}
//                                         </span>
//                                     </div>
//                                 </div>
//                                 <div className="p-col-12 p-md-6">
//                                     <div className="p-field">
//                                         <label>
//                                             <strong>Reverse Charge:</strong>
//                                         </label>
//                                         <span className="ml-2">
//                                             {invoice.invrecord.reversecharge ===
//                                                 0 ||
//                                             invoice.invrecord.reversecharge ===
//                                                 null
//                                                 ? "No"
//                                                 : "Yes"}
//                                         </span>
//                                     </div>
//                                 </div>
//                             </div>
//                         </Panel>
//                     </div>

//                     {/* Invoice Summary */}
//                     <div className="p-col-12 p-md-6">
//                         <Panel header="Invoice Summary" className="p-shadow-2">
//                             <div className="p-field">
//                                 <label>
//                                     <strong>Total Taxable Amount:</strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     ₹
//                                     {invoice.invrecord.contents
//                                         .reduce(
//                                             (sum, item) =>
//                                                 sum + item.taxableAmount,
//                                             0
//                                         )
//                                         .toFixed(2)}
//                                 </span>
//                             </div>
//                             <div className="p-field">
//                                 <label>
//                                     <strong>Total Discount:</strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     ₹
//                                     {invoice.invrecord.contents
//                                         .reduce(
//                                             (sum, item) =>
//                                                 sum +
//                                                 (item.discountAmount || 0),
//                                             0
//                                         )
//                                         .toFixed(2)}
//                                 </span>
//                             </div>

//                             {invoice.invrecord.taxstate ===
//                             invoice.invrecord.sourcestate ? (
//                                 <>
//                                     <div className="p-field">
//                                         <label>
//                                             <strong>Total CGST:</strong>
//                                         </label>
//                                         <span className="ml-2">
//                                             ₹
//                                             {(
//                                                 invoice.invrecord.contents.reduce(
//                                                     (sum, item) =>
//                                                         sum + item.gstamount,
//                                                     0
//                                                 ) / 2
//                                             ).toFixed(2)}
//                                         </span>
//                                     </div>
//                                     <div className="p-field">
//                                         <label>
//                                             <strong>Total SGST:</strong>
//                                         </label>
//                                         <span className="ml-2">
//                                             ₹
//                                             {(
//                                                 invoice.invrecord.contents.reduce(
//                                                     (sum, item) =>
//                                                         sum + item.gstamount,
//                                                     0
//                                                 ) / 2
//                                             ).toFixed(2)}
//                                         </span>
//                                     </div>
//                                 </>
//                             ) : (
//                                 <div className="p-field">
//                                     <label>
//                                         <strong>Total IGST:</strong>
//                                     </label>
//                                     <span className="ml-2">
//                                         ₹
//                                         {invoice.invrecord.contents
//                                             .reduce(
//                                                 (sum, item) =>
//                                                     sum + item.gstamount,
//                                                 0
//                                             )
//                                             .toFixed(2)}
//                                     </span>
//                                 </div>
//                             )}

//                             <div className="p-field">
//                                 <label>
//                                     <strong>Total Invoice Amount:</strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     ₹{invoice.invrecord.invoicetotal}
//                                 </span>
//                             </div>
//                             <div className="p-field">
//                                 <label>
//                                     <strong>Total Paid Amount:</strong>
//                                 </label>
//                                 <span className="ml-2">
//                                     ₹{invoice.invrecord.amountpaid}
//                                 </span>
//                             </div>
//                         </Panel>
//                     </div>
//                 </div>
//                 <Divider />
//                 {/* Mode of Transportation */}
//                 {(invoice.invrecord.transportationmode ||
//                     invoice.invrecord.transportationmode == "None" ||
//                     invoice.invrecord.vehicleno ||
//                     invoice.invrecord.dateofsupply) && (
//                     <Fieldset
//                         legend="Mode of Transportation"
//                         className="p-mb-4 p-shadow-2"
//                     >
//                         <div className="p-grid">
//                             <div className="p-col-12 p-md-6">
//                                 {invoice.invrecord.transportationmode && (
//                                     <div className="p-field">
//                                         <label className="p-d-block">
//                                             <strong>
//                                                 Transportation Mode :{" "}
//                                             </strong>
//                                         </label>
//                                         <span>
//                                             {invoice.invrecord
//                                                 .transportationmode || "N/A"}
//                                         </span>
//                                     </div>
//                                 )}
//                                 {invoice.invrecord.vehicleno && (
//                                     <div className="p-field">
//                                         <label className="p-d-block">
//                                             <strong>Vehicle No : </strong>
//                                         </label>
//                                         <span>
//                                             {invoice.invrecord.vehicleno ||
//                                                 "N/A"}
//                                         </span>
//                                     </div>
//                                 )}
//                             </div>
//                             <div className="p-col-12 p-md-6">
//                                 {invoice.invrecord.dateofsupply && (
//                                     <div className="p-field">
//                                         <label className="p-d-block">
//                                             <strong>Date of Supply : </strong>
//                                         </label>
//                                         <span>
//                                             {invoice.invrecord.dateofsupply
//                                                 ? new Date(
//                                                       invoice.invrecord.dateofsupply
//                                                   )
//                                                       .toLocaleDateString(
//                                                           "en-GB",
//                                                           {
//                                                               day: "2-digit",
//                                                               month: "2-digit",
//                                                               year: "numeric",
//                                                           }
//                                                       )
//                                                       .split("/")
//                                                       .join(" /")
//                                                 : "N/A"}
//                                         </span>
//                                     </div>
//                                 )}
//                             </div>
//                         </div>
//                     </Fieldset>
//                 )}
//                 <Divider />
//                 {/* Invoice Summary */}
//                 <div className="p-field">
//                     <label className="p-d-block">
//                         <strong>Total in Words : </strong>
//                     </label>
//                     <span>{invoice.invrecord.invoicetotalword || "N/A"}</span>
//                 </div>
//                 <div className="p-field">
//                     <label className="p-d-block">
//                         <strong>Narration : </strong>
//                     </label>
//                     <span>{invoice.invrecord.invnarration || ""}</span>
//                 </div>
//             </Card>

//             {invoice && combinedData && (
//                 <div className="flex">
//                     {/* PDF download button - only show when we have combined data */}
//                     <PDFDownloadLink
//                         document={<InvoicePDF data={combinedData} />}
//                         fileName={`invoice_${invoice.invrecord.invoiceno}.pdf`}
//                     >
//                         {({ loading }) => (
//                             <Button
//                                 text
//                                 raised
//                                 label={loading ? "Generating PDF..." : "Print"}
//                                 icon={
//                                     loading
//                                         ? "pi pi-spinner pi-spin"
//                                         : "pi pi-print"
//                                 }
//                                 className="w-full sm:w-auto bg-blue-600 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
//                                 disabled={loading}
//                             />
//                         )}
//                     </PDFDownloadLink>
//                 </div>
//             )}

//             {invoice && !combinedData && (
//                 <div className="flex">
//                     <Button
//                         text
//                         raised
//                         label="Loading PDF data..."
//                         icon="pi pi-spinner pi-spin"
//                         className="w-full sm:w-auto bg-gray-400 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
//                         disabled={true}
//                     />
//                 </div>
//             )}
//         </div>
//     );
// };

// export default InvoiceView;

"use client";
import React, { useState, useEffect } from "react";
import { Card } from "primereact/card";
import { DataTable } from "primereact/datatable";
import { Column } from "primereact/column";
import { ProgressSpinner } from "primereact/progressspinner";
import { Message } from "primereact/message";
import { Fieldset } from "primereact/fieldset";
import { Panel } from "primereact/panel";
import { Divider } from "primereact/divider";
import { AxiosError } from "axios";
import { getOrgDataField } from "../../../utils/cookies";
import { useEnvContext } from "../../../contexts/EnvContextProvider";
import "../../../styles/olkcss.scss";
import { Button } from "primereact/button";
import { PDFDownloadLink } from "@react-pdf/renderer";
import InvoicePDF from "./InvoicePdf";
import apiCall from "../../../utils/apiCallService";

// Define invoice preferences interface
interface InvoicePrefs {
    logo?: string;
    sign?: string;
    autoinvno?: number;
    tandc?: string;
}

export interface orgFields {
    bankdetails: null | string;
    gstin: string;
    orgpan: string;
    orgaddr: string;
    orgpincode: string;
    invoice_preferences?: InvoicePrefs;
}

export interface details {
    bisdetails: orgFields; // Match InvoicePdf.tsx expectation
    olkstatus?: number; // Matches JSON structure
}

export interface InvoiceContent {
    gsflag: number;
    gstflag: number;
    gstrate: number;
    quantity: number;
    gstamount: number;
    productcode: number;
    productname: string;
    freeQuantity: number;
    pricePerUnit: number;
    productAmount: number;
    taxableAmount: number;
    discountAmount: number;
}

export interface InvoiceParty {
    custname: string;
    custaddr: string;
    custphone: string;
    custemail: string | null;
    state: string;
    pincode: string;
}

export interface Consignee {
    name?: string;
    state?: string;
    address?: string;
    pincode?: string;
}

export interface InvoiceRecord {
    invid: number;
    invoiceno: string;
    ewaybillno: string | null;
    invoicedate: string;
    invnarration: string | null;
    taxflag: number;
    contents: InvoiceContent[];
    amountpaid: string;
    invoicetotal: string;
    icflag: number;
    roundoffflag: number;
    lockflag: number;
    discflag: number;
    taxstate: string | null;
    sourcestate: string | null;
    orgstategstin: string | null;
    attachment: string | null;
    attachmentcount: number;
    orgcode: number;
    custid: number;
    consignee: Consignee | null;
    reverserecharge: string | null;
    bankdetails: string | null;
    transportationmode: string | null;
    vehicleno: string | null;
    dateofsupply: string | null;
    paymentmode: number;
    reversecharge: number | null;
    address: string | null;
    pincode: string | null;
    inoutflag: number;
    originaldate: string | null;
    invoicetotalword: string | null;
}

export interface ApiResponse {
    invrecord: InvoiceRecord;
    custdetails?: InvoiceParty; // Made optional to handle potential absence
}

interface CombinedData extends ApiResponse {
    orgDetails: details | null;
}

interface InvoiceViewProps {
    invoiceId: string;
}

const formatIndianCurrency = (amount: number | string): string => {
    const num = typeof amount === "string" ? parseFloat(amount) : amount;
    if (isNaN(num)) return "₹0.00";

    // Convert to fixed 2 decimal places
    const fixedNum = num.toFixed(2);
    const [integerPart, decimalPart] = fixedNum.split(".");

    // Apply Indian number formatting (lakhs and crores)
    const lastThreeDigits = integerPart.slice(-3);
    const otherDigits = integerPart.slice(0, -3);

    if (otherDigits !== "") {
        const formattedOtherDigits = otherDigits.replace(
            /\B(?=(\d{2})+(?!\d))/g,
            ","
        );
        return `${formattedOtherDigits},${lastThreeDigits}.${decimalPart}`;
    } else {
        return `${lastThreeDigits}.${decimalPart}`;
    }
};

const InvoiceView: React.FC<InvoiceViewProps> = ({ invoiceId }) => {
    const { OLK_PATH } = useEnvContext();
    const [invoice, setInvoice] = useState<ApiResponse | null>(null);
    const [combinedData, setCombinedData] = useState<CombinedData | null>(null);
    const [loading, setLoading] = useState<boolean>(true);
    const [error, setError] = useState<string | null>(null);
    const orgname = getOrgDataField("orgname");
    const orgCode = Number(getOrgDataField("orgcode"));

    useEffect(() => {
        const fetchInvoice = async () => {
            if (!OLK_PATH) {
                setError("API base URL is not configured");
                setLoading(false);
                return;
            }

            if (!invoiceId) {
                setError("No invoice ID provided");
                setLoading(false);
                return;
            }

            setLoading(true);
            setError(null);
            try {
                const response = await apiCall<ApiResponse>(
                    "GET",
                    `${OLK_PATH}/invoice/findinvoice?invid=${invoiceId}`
                );
                if (!response.data?.invrecord) {
                    throw new Error("No invoice record found");
                }
                setInvoice(response.data);
            } catch (err: unknown) {
                let errorMessage = "Failed to fetch invoice details.";
                if (err instanceof AxiosError) {
                    if (err.response?.status === 404) {
                        errorMessage = `Invoice not found for invid: ${invoiceId}`;
                    } else {
                        errorMessage = `Failed to fetch invoice: ${err.message}`;
                    }
                } else if (err instanceof Error) {
                    errorMessage = `Failed to fetch invoice: ${err.message}`;
                }

                setError(errorMessage);
                console.error("Error details:", err);
            } finally {
                setLoading(false);
            }
        };

        fetchInvoice();
    }, [invoiceId, OLK_PATH]);

    // Function to fetch orgDetails and combine with invoice
    useEffect(() => {
        const fetchOrgDetails = async () => {
            if (!invoice) {
                return;
            }

            if (!OLK_PATH || !orgCode) {
                console.error("Missing OLK_PATH or orgCode");
                return;
            }

            try {
                const orgResponse = await apiCall<details>(
                    "GET",
                    `${OLK_PATH}/organisations/bdt?orgcode=${orgCode}`
                );
                const combined: CombinedData = {
                    ...invoice,
                    orgDetails: orgResponse.data,
                };
                setCombinedData(combined);
            } catch (err: unknown) {
                console.error("Error fetching orgDetails:", err);
                // Set combined data without org details to prevent blocking
                setCombinedData({
                    ...invoice,
                    orgDetails: null,
                });
            }
        };

        // Only fetch org details if we have invoice data
        if (invoice && invoice.invrecord) {
            fetchOrgDetails();
        }
    }, [invoice, OLK_PATH, orgCode]);

    if (loading) {
        return (
            <ProgressSpinner
                style={{ display: "block", margin: "2rem auto" }}
            />
        );
    }

    if (error) {
        return (
            <Message
                severity="error"
                text={error}
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    if (!invoice || !invoice.invrecord) {
        return (
            <Message
                severity="warn"
                text="No invoice data available."
                style={{ display: "block", margin: "2rem" }}
            />
        );
    }

    return (
        <div className="invoice-view p-m-4">
            <style>{`
        .invoice-view {
          padding: 0.5rem;
        }

        /* Mobile-specific styling for Products/Services table */
        @media (max-width: 768px) {
          .invoice-products-table {
            display: none; /* Hide DataTable on mobile */
          }

          .invoice-products-card-container {
            display: flex;
            flex-direction: column;
            gap: 0.75rem;
            padding: 0.25rem;
            margin: 0 -0.25rem;
          }

          .invoice-product-card {
            background: #ffffff;
            border: 1px solid #e2e8f0;
            border-radius: 12px;
            padding: 1rem;
            min-height: 320px;
            width: 100%;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
          }

          .invoice-product-card:hover {
            transform: translateY(-1px);
            box-shadow: 0 6px 16px rgba(0, 0, 0, 0.08);
          }

          .invoice-product-card div {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 0.75rem;
            padding: 0.25rem 0;
          }

          .invoice-product-card label {
            font-weight: 600;
            color: #1f2937;
            font-size: 0.95rem;
            flex-shrink: 0;
            min-width: 100px;
          }

          .invoice-product-card span {
            color: #4b5563;
            font-size: 0.95rem;
            text-align: right;
            word-break: break-word;
          }

          .invoice-product-card .product-name {
            font-size: 1.1rem;
            font-weight: 700;
            color: #6366f1;
            text-align: right;
          }

          .invoice-product-card .total-amount {
            font-size: 1.05rem;
            font-weight: 700;
            color: #059669;
          }

          .invoice-product-card .divider-line {
            border-top: 1px solid #e5e7eb;
            margin: 0.5rem 0;
            padding: 0;
          }
        }

        /* Desktop-specific styling */
        @media (min-width: 769px) {
          .invoice-products-card-container {
            display: none;
          }

          .invoice-products-table {
            display: block;
          }
        }
      `}</style>
            <Card
                title={
                    invoice.invrecord.inoutflag === 15
                        ? "Sales Invoice"
                        : invoice.invrecord.inoutflag === 9
                        ? "Purchase Invoice"
                        : "Unknown Invoice"
                }
            >
                {" "}
                <div className="invoice-header ">
                    <div className="invoice-header">{orgname}</div>
                    <div className="invoice-header">{`Invoice No : ${invoice.invrecord.invoiceno}`}</div>
                    <div className="invoice-header">
                        {`Invoice Date : ${new Date(
                            invoice.invrecord.invoicedate
                        )
                            .toLocaleDateString("en-GB", {
                                day: "2-digit",
                                month: "2-digit",
                                year: "numeric",
                            })
                            .split("/")
                            .join("-")}`}
                    </div>
                </div>
                <Divider />
                {/* Customer and Consignee Details */}
                <div className="p-grid invoice-section">
                    <div className="p-col-6 p-md-6">
                        <Fieldset
                            legend={
                                invoice.invrecord.inoutflag === 15
                                    ? "Customer Details"
                                    : "Supplier details"
                            }
                            className="p-shadow-2"
                        >
                            <div className="p-field">
                                <label>
                                    <strong>Name : </strong>
                                </label>
                                <span className="ml-2">
                                    {invoice.custdetails?.custname || "N/A"}
                                </span>
                            </div>

                            <div className="p-grid p-formgrid">
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Phone : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.custphone ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Email : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.custemail ||
                                            "N/A"}
                                    </span>
                                </div>
                            </div>

                            <div className="p-field">
                                <label>
                                    <strong>Address : </strong>
                                </label>
                                <span className="ml-2">
                                    {invoice.custdetails?.custaddr || "N/A"}
                                </span>
                            </div>

                            <div className="p-grid p-formgrid">
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>State : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.state || "N/A"}
                                    </span>
                                </div>
                                <div className="p-col-3 p-field">
                                    <label>
                                        <strong>Pincode : </strong>
                                    </label>
                                    <span className="ml-2">
                                        {invoice.custdetails?.pincode || "N/A"}
                                    </span>
                                </div>
                            </div>
                        </Fieldset>
                    </div>
                    <Divider />
                    {invoice.invrecord.consignee && (
                        <div className="p-col-12 p-md-6">
                            <Fieldset
                                legend="Consignee Details"
                                className="p-shadow-2"
                            >
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Name : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.name ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.address ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.state ||
                                            "N/A"}
                                    </span>
                                </div>
                                <div className="p-field">
                                    <label className="p-d-block">
                                        <strong>Consignee Address : </strong>
                                    </label>
                                    <span>
                                        {invoice.invrecord.consignee?.pincode ||
                                            "N/A"}
                                    </span>
                                </div>
                            </Fieldset>
                        </div>
                    )}
                </div>
                <Divider />
                {/* Product Table */}
                <Panel header="Products/Services" className="p-mb-2 p-shadow-2">
                    <DataTable
                        value={invoice.invrecord.contents}
                        className="p-datatable-sm invoice-products-table"
                        scrollable
                        scrollHeight="flex"
                        resizableColumns
                        columnResizeMode="fit"
                        showGridlines
                        stripedRows
                        size="small"
                    >
                        <Column
                            field="productname"
                            header="Product Name"
                            className="p-text-bold"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="quantity"
                            header="Quantity"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="freeQuantity"
                            header="Free Qty"
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="pricePerUnit"
                            header="Price/Unit"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.pricePerUnit.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="discountAmount"
                            header="Discount"
                            body={(rowData) => {
                                if (invoice.invrecord.discflag === 1) {
                                    // Amount discount
                                    return `${(
                                        rowData.discountAmount || 0
                                    ).toFixed(2)}`;
                                } else if (invoice.invrecord.discflag === 16) {
                                    // Percentage discount - calculate percentage from discountAmount
                                    const baseValue =
                                        rowData.quantity * rowData.pricePerUnit;
                                    if (
                                        baseValue > 0 &&
                                        rowData.discountAmount > 0
                                    ) {
                                        const discountPercentage =
                                            (rowData.discountAmount /
                                                baseValue) *
                                            100;
                                        return `${discountPercentage.toFixed(
                                            2
                                        )}%`;
                                    }
                                    return "0.00%";
                                } else {
                                    // Fallback for unknown discount types
                                    return `${(
                                        rowData.discountAmount || 0
                                    ).toFixed(2)}`;
                                }
                            }}
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="taxableAmount"
                            header="Taxable Amt"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.taxableAmount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="gstrate"
                            header="GST Rate"
                            body={(rowData) => `${rowData.gstrate}%`}
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="gstamount"
                            header="GST Amount"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.gstamount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                        <Column
                            field="productAmount"
                            header="Total Amount"
                            body={(rowData) =>
                                `${formatIndianCurrency(
                                    rowData.productAmount.toFixed(2)
                                )}`
                            }
                            headerStyle={{ backgroundColor: "#bfdbfe" }}
                        />
                    </DataTable>
                    {/* Card Layout for Mobile */}
                    <div className="invoice-products-card-container">
                        {invoice.invrecord.contents.map((product, index) => (
                            <div key={index} className="invoice-product-card">
                                <div>
                                    <label>Product:</label>
                                    <span className="product-name">
                                        {product.productname}
                                    </span>
                                </div>
                                <div className="divider-line"></div>
                                <div>
                                    <label>Quantity:</label>
                                    <span>{product.quantity}</span>
                                </div>
                                <div>
                                    <label>Free Qty:</label>
                                    <span>{product.freeQuantity}</span>
                                </div>
                                <div>
                                    <label>Price/Unit:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.pricePerUnit.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div>
                                    <label>Discount:</label>
                                    <span>
                                        {invoice.invrecord.discflag === 1
                                            ? `₹${(
                                                  product.discountAmount || 0
                                              ).toFixed(2)}`
                                            : `${(
                                                  product.discountAmount || 0
                                              ).toFixed(2)}%`}
                                    </span>
                                </div>
                                <div>
                                    <label>Taxable Amt:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.taxableAmount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div>
                                    <label>GST Rate:</label>
                                    <span>{product.gstrate}%</span>
                                </div>
                                <div>
                                    <label>GST Amount:</label>
                                    <span>
                                        {formatIndianCurrency(
                                            product.gstamount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                                <div className="divider-line"></div>
                                <div>
                                    <label>Total Amount:</label>
                                    <span className="total-amount">
                                        {formatIndianCurrency(
                                            product.productAmount.toFixed(2)
                                        )}
                                    </span>
                                </div>
                            </div>
                        ))}
                    </div>
                </Panel>
                <Divider />
                {/* Payment Details and Invoice Summary Side by Side */}
                <div className="p-mb-2 p-grid">
                    {/* Payment Details */}
                    <div className="p-mb-2 p-col-12 p-md-6">
                        <Panel
                            header="Payment Details"
                            className="p-mb-2 p-shadow-2"
                        >
                            <div className="p-grid">
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Payment Mode:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.paymentmode ===
                                            15
                                                ? "On Credit"
                                                : invoice.invrecord
                                                      .paymentmode === 3
                                                ? "Cash Received"
                                                : invoice.invrecord
                                                      .paymentmode === 2
                                                ? "Bank Transfer"
                                                : "Unknown"}
                                        </span>
                                    </div>
                                </div>
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Bank Details:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.bankdetails ||
                                                "N/A"}
                                        </span>
                                    </div>
                                </div>
                                <div className="p-col-12 p-md-6">
                                    <div className="p-field">
                                        <label>
                                            <strong>Reverse Charge:</strong>
                                        </label>
                                        <span className="ml-2">
                                            {invoice.invrecord.reversecharge ===
                                                0 ||
                                            invoice.invrecord.reversecharge ===
                                                null
                                                ? "No"
                                                : "Yes"}
                                        </span>
                                    </div>
                                </div>
                            </div>
                        </Panel>
                    </div>

                    <Divider />
                    {/* Invoice Summary */}
                    <div className="p-col-12 p-md-6">
                        <Panel header="Invoice Summary" className="p-shadow-2">
                            <div className="p-field">
                                <label>
                                    <strong>Total Taxable Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        invoice.invrecord.contents.reduce(
                                            (sum, item) =>
                                                sum + item.taxableAmount,
                                            0
                                        )
                                    )}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Discount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        invoice.invrecord.contents.reduce(
                                            (sum, item) =>
                                                sum +
                                                (item.discountAmount || 0),
                                            0
                                        )
                                    )}
                                </span>
                            </div>

                            {invoice.invrecord.taxstate ===
                            invoice.invrecord.sourcestate ? (
                                <>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total CGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {formatIndianCurrency(
                                                invoice.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            )}
                                        </span>
                                    </div>
                                    <div className="p-field">
                                        <label>
                                            <strong>Total SGST:</strong>
                                        </label>
                                        <span className="ml-2">
                                            ₹
                                            {formatIndianCurrency(
                                                invoice.invrecord.contents.reduce(
                                                    (sum, item) =>
                                                        sum + item.gstamount,
                                                    0
                                                ) / 2
                                            )}
                                        </span>
                                    </div>
                                </>
                            ) : (
                                <div className="p-field">
                                    <label>
                                        <strong>Total IGST:</strong>
                                    </label>
                                    <span className="ml-2">
                                        ₹
                                        {formatIndianCurrency(
                                            invoice.invrecord.contents.reduce(
                                                (sum, item) =>
                                                    sum + item.gstamount,
                                                0
                                            )
                                        )}
                                    </span>
                                </div>
                            )}

                            <div className="p-field">
                                <label>
                                    <strong>Total Invoice Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        invoice.invrecord.invoicetotal
                                    )}
                                </span>
                            </div>
                            <div className="p-field">
                                <label>
                                    <strong>Total Paid Amount:</strong>
                                </label>
                                <span className="ml-2">
                                    ₹
                                    {formatIndianCurrency(
                                        invoice.invrecord.amountpaid
                                    )}
                                </span>
                            </div>
                        </Panel>
                    </div>
                </div>
                <Divider />
                {/* Mode of Transportation */}
                {(invoice.invrecord.transportationmode ||
                    invoice.invrecord.transportationmode == "None" ||
                    invoice.invrecord.vehicleno ||
                    invoice.invrecord.dateofsupply) && (
                    <Fieldset
                        legend="Mode of Transportation"
                        className="p-mb-4 p-shadow-2"
                    >
                        <div className="p-grid">
                            <div className="p-col-12 p-md-6">
                                {invoice.invrecord.transportationmode && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>
                                                Transportation Mode :{" "}
                                            </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord
                                                .transportationmode || "N/A"}
                                        </span>
                                    </div>
                                )}
                                {invoice.invrecord.vehicleno && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>Vehicle No : </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord.vehicleno ||
                                                "N/A"}
                                        </span>
                                    </div>
                                )}
                            </div>
                            <div className="p-col-12 p-md-6">
                                {invoice.invrecord.dateofsupply && (
                                    <div className="p-field">
                                        <label className="p-d-block">
                                            <strong>Date of Supply : </strong>
                                        </label>
                                        <span>
                                            {invoice.invrecord.dateofsupply
                                                ? new Date(
                                                      invoice.invrecord.dateofsupply
                                                  )
                                                      .toLocaleDateString(
                                                          "en-GB",
                                                          {
                                                              day: "2-digit",
                                                              month: "2-digit",
                                                              year: "numeric",
                                                          }
                                                      )
                                                      .split("/")
                                                      .join(" /")
                                                : "N/A"}
                                        </span>
                                    </div>
                                )}
                            </div>
                        </div>
                    </Fieldset>
                )}
                <Divider />
                {/* Invoice Summary */}
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Total in Words : </strong>
                    </label>
                    <span>{invoice.invrecord.invoicetotalword || "N/A"}</span>
                </div>
                <div className="p-field">
                    <label className="p-d-block">
                        <strong>Narration : </strong>
                    </label>
                    <span>{invoice.invrecord.invnarration || ""}</span>
                </div>
            </Card>

            {invoice && combinedData && (
                <div className="flex">
                    {/* PDF download button - only show when we have combined data */}
                    <PDFDownloadLink
                        document={<InvoicePDF data={combinedData} />}
                        fileName={`invoice_${invoice.invrecord.invoiceno}.pdf`}
                    >
                        {({ loading }) => (
                            <Button
                                text
                                raised
                                label={loading ? "Generating PDF..." : "Print"}
                                icon={
                                    loading
                                        ? "pi pi-spinner pi-spin"
                                        : "pi pi-print"
                                }
                                className="w-full sm:w-auto bg-blue-600 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                                disabled={loading}
                            />
                        )}
                    </PDFDownloadLink>
                </div>
            )}

            {invoice && !combinedData && (
                <div className="flex">
                    <Button
                        text
                        raised
                        label="Loading PDF data..."
                        icon="pi pi-spinner pi-spin"
                        className="w-full sm:w-auto bg-gray-400 text-lg md:text-2xl px-5 py-3 olk-button mt-3"
                        disabled={true}
                    />
                </div>
            )}
        </div>
    );
};

export default InvoiceView;
